# TableWithView组件重构方案

## 1. 重构背景与目标

### 1.1 重构原因
当前系统中`SidebarTreeView`和`TableViewWithSearch`是两个独立的组件，但在业务逻辑上它们应该是一个整体：
- **左侧视图列表**：实际保存的是搜索条件，用于快速切换不同的数据视图
- **右侧表格**：根据左侧选中的视图显示对应的数据
- **业务关联性**：点击左侧视图应该更新右侧表格的数据，这是一个完整的业务功能

### 1.2 重构目标
1. **组件合并**：将`SidebarTreeView`和`TableViewWithSearch`合并为一个`TableWithView`组件
2. **布局调整**：将`TopDownLayout`从上下布局改为上中下三块布局
3. **数据联动**：实现左侧视图与右侧表格的数据联动
4. **配置简化**：简化Schema配置，减少重复配置

## 2. 组件设计方案

### 2.1 TableWithView组件结构
```
TableWithView
├── 左侧区域 (ViewPanel)
│   ├── 搜索框
│   ├── 视图树
│   └── 视图管理功能
└── 右侧区域 (TablePanel)
    ├── 工具栏
    ├── 搜索表单
    ├── 分组标签
    ├── 数据表格
    └── 分页器
```

### 2.2 组件接口设计
```typescript
export interface TableWithViewProps {
  // 基础配置
  componentId?: string;
  style?: React.CSSProperties;
  className?: string;
  
  // 视图配置
  viewConfig?: {
    width?: number;
    searchPlaceholder?: string;
    showSearch?: boolean;
  };
  
  // 表格配置 (继承自TableViewWithSearch)
  title?: string;
  columns: TableColumn[];
  searchFields?: SearchField[];
  toolbarActions?: ToolbarAction[];
  quickSearch?: QuickSearchConfig;
  groupTabs?: GroupTab[];
  rowActions?: TableAction[];
  pagination?: PaginationConfig;
  rowSelection?: RowSelectionConfig;
  
  // 事件配置
  events?: Record<string, any>;
  
  // 事件处理器
  onViewSelect?: (viewKey: string, viewData: any) => void;
  onTableDataChange?: (data: any[]) => void;
  onSearch?: (searchValues: Record<string, any>) => void;
  onTableChange?: (pagination: any, filters: any, sorter: any) => void;
}
```

### 2.3 数据流设计
1. **视图数据加载**：组件挂载时通过`onMount`事件加载视图列表
2. **视图选择**：用户点击左侧视图时触发`onViewSelect`事件
3. **表格数据更新**：根据选中的视图，通过`onViewDataLoad`事件加载对应的表格数据
4. **搜索联动**：右侧搜索条件变化时，可以影响左侧视图的选中状态

## 3. TopDownLayout布局重构

### 3.1 新的布局结构（简化版）
```
TopDownLayout (上中下三段式布局)
├── Header (顶部导航)
├── Main (主体内容区域)
└── Footer (底部状态栏)
```

### 3.2 简化的布局接口
```typescript
export interface TopDownLayoutProps {
  header?: React.ReactNode;
  footer?: React.ReactNode;
  children?: React.ReactNode;  // 主体内容，通常是TableWithView
  headerHeight?: number;
  footerHeight?: number;
  style?: React.CSSProperties;
  className?: string;
}
```

### 3.3 布局实现简化
- 移除复杂的组件识别逻辑
- 移除sidebar相关的所有代码
- 采用简单的三段式布局：header + main + footer

## 4. Schema配置调整

### 4.1 当前配置结构
```json
{
  "components": [
    {
      "id": "layout",
      "type": "TopDownLayout",
      "children": [
        {"id": "top_nav", "type": "TopNavigation"},
        {"id": "sidebar", "type": "SidebarTreeView"},
        {"id": "api_table", "type": "TableViewWithSearch"},
        {"id": "status_bar", "type": "StatusBar"}
      ]
    }
  ]
}
```

### 4.2 调整后配置结构
```json
{
  "components": [
    {
      "id": "layout",
      "type": "TopDownLayout",
      "children": [
        {"id": "top_nav", "type": "TopNavigation"},
        {"id": "table_with_view", "type": "TableWithView"},
        {"id": "status_bar", "type": "StatusBar"}
      ]
    }
  ]
}
```

## 5. 实现步骤

### 5.1 第一阶段：组件开发
1. **创建TableWithView组件**
   - 合并SidebarTreeView和TableViewWithSearch的功能
   - 实现左右布局
   - 实现数据联动逻辑

2. **更新组件元数据**
   - 在business/index.ts中添加TableWithView的元数据
   - 定义组件的属性配置和事件配置

### 5.2 第二阶段：布局调整
1. **修改TopDownLayout组件**
   - 添加middle区域支持
   - 调整布局逻辑和样式
   - 保持向后兼容性

2. **更新布局元数据**
   - 在layout/index.ts中更新TopDownLayout的元数据

### 5.3 第三阶段：Schema更新
1. **更新api-management.json**
   - 使用新的TableWithView组件
   - 合并原有的视图和表格配置
   - 调整事件配置

2. **测试验证**
   - 验证组件功能正常
   - 验证数据联动正确
   - 验证布局显示正常

## 6. 组件清理

### 6.1 移除旧组件
- 直接删除`SidebarTreeView`组件
- 直接删除`TableViewWithSearch`组件
- 清理相关的类型定义和元数据

### 6.2 简化导出
- 只保留必要的组件导出
- 清理unused的类型和工具函数

## 7. 技术细节

### 7.1 状态管理
- 使用React.useState管理组件内部状态
- 通过useEventHandler处理事件通信
- 使用useMappedData获取数据

### 7.2 样式设计
- 左侧视图面板：固定宽度240px，可配置
- 右侧表格面板：自适应剩余宽度
- 响应式设计：小屏幕时可折叠视图面板

### 7.3 性能优化
- 使用React.memo优化渲染性能
- 使用useMemo缓存计算结果
- 懒加载表格数据

## 8. 风险评估

### 8.1 技术风险
- **低风险**：基于现有组件合并，技术实现相对简单
- **数据联动**：需要仔细设计事件机制，确保数据同步正确

### 8.2 业务风险
- **低风险**：不改变现有业务逻辑，只是组件结构调整
- **用户体验**：需要确保新组件的交互体验不低于原有组件

## 9. 后续优化

### 9.1 功能增强
- 支持视图的增删改操作
- 支持视图的导入导出
- 支持自定义视图配置

### 9.2 性能优化
- 虚拟滚动支持大数据量
- 数据缓存机制
- 增量更新优化

---

**注意**：本方案需要在实施前进行详细的技术评审，确保所有细节都考虑周全。建议分阶段实施，每个阶段都要进行充分的测试验证。
