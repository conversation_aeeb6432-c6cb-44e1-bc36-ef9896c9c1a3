# 简化版重构实施方案

## 1. 重构原则

### 1.1 核心原则
- **无历史包袱**：直接删除旧组件，不考虑兼容性
- **简洁高效**：架构简单直接，避免过度设计
- **一步到位**：直接实现目标架构，不做渐进式迁移

### 1.2 技术决策
- 删除`SidebarTreeView`和`TableViewWithSearch`组件
- 重构`TopDownLayout`为简单的三段式布局
- 创建全新的`TableWithView`组件
- 简化Schema配置结构

## 2. 文件操作清单

### 2.1 删除文件
```bash
# 删除旧组件
rm packages/shared/src/components/business/SidebarTreeView.tsx
rm packages/shared/src/components/business/TableViewWithSearch.tsx

# 清理类型定义（如果有独立文件）
rm packages/shared/src/components/business/types/SidebarTreeView.ts
rm packages/shared/src/components/business/types/TableViewWithSearch.ts
```

### 2.2 创建新文件
```bash
# 创建TableWithView组件目录
mkdir -p packages/shared/src/components/business/TableWithView/components/ViewPanel
mkdir -p packages/shared/src/components/business/TableWithView/components/TablePanel
mkdir -p packages/shared/src/components/business/TableWithView/hooks

# 新文件列表
packages/shared/src/components/business/TableWithView/
├── index.tsx                     # 导出入口
├── TableWithView.tsx             # 主组件
├── types.ts                      # 类型定义
├── constants.ts                  # 常量定义
├── components/
│   ├── ViewPanel/
│   │   ├── index.tsx
│   │   ├── ViewPanel.tsx
│   │   ├── ViewItem.tsx
│   │   └── ViewSearch.tsx
│   └── TablePanel/
│       ├── index.tsx
│       └── TablePanel.tsx
└── hooks/
    ├── useTableWithView.ts       # 主业务逻辑
    ├── useViewManager.ts         # 视图管理
    ├── useTableManager.ts        # 表格管理
    └── useDataCache.ts           # 数据缓存
```

### 2.3 修改文件
```bash
# 重构布局组件
packages/shared/src/components/layout/TopDownLayout.tsx

# 更新导出文件
packages/shared/src/components/business/index.ts
packages/shared/src/components/layout/index.ts

# 更新Schema文件
examples/schemas/api-management.json
```

## 3. 核心组件实现

### 3.1 TableWithView主组件
```typescript
// packages/shared/src/components/business/TableWithView/TableWithView.tsx
import React from 'react';
import { ViewPanel } from './components/ViewPanel';
import { TablePanel } from './components/TablePanel';
import { useTableWithView } from './hooks/useTableWithView';
import { TableWithViewProps } from './types';

export const TableWithView: React.FC<TableWithViewProps> = (props) => {
  const {
    viewState,
    tableState,
    handleViewSelect,
    handleTableSearch
  } = useTableWithView(props);

  return (
    <div className="lowcode-table-with-view" style={{ display: 'flex', height: '100%' }}>
      <ViewPanel
        {...viewState}
        onSelect={handleViewSelect}
        width={props.viewConfig?.width || 240}
      />
      <TablePanel
        {...tableState}
        {...props}
        onSearch={handleTableSearch}
      />
    </div>
  );
};
```

### 3.2 简化的TopDownLayout
```typescript
// packages/shared/src/components/layout/TopDownLayout.tsx
import React from 'react';

export interface TopDownLayoutProps {
  header?: React.ReactNode;
  footer?: React.ReactNode;
  children?: React.ReactNode;
  headerHeight?: number;
  footerHeight?: number;
  style?: React.CSSProperties;
  className?: string;
}

export const TopDownLayout: React.FC<TopDownLayoutProps> = ({
  header,
  footer,
  children,
  headerHeight = 64,
  footerHeight = 32,
  style,
  className,
}) => {
  return (
    <div 
      className={`lowcode-topdown-layout ${className || ''}`}
      style={{
        display: 'flex',
        flexDirection: 'column',
        height: '100vh',
        overflow: 'hidden',
        ...style,
      }}
    >
      {header && (
        <header style={{
          minHeight: `${headerHeight}px`,
          flexShrink: 0,
          backgroundColor: '#fff',
          borderBottom: '1px solid #f0f0f0',
        }}>
          {header}
        </header>
      )}
      
      <main style={{
        flex: 1,
        overflow: 'hidden',
        minHeight: 0,
      }}>
        {children}
      </main>
      
      {footer && (
        <footer style={{
          height: `${footerHeight}px`,
          flexShrink: 0,
          backgroundColor: '#fff',
          borderTop: '1px solid #f0f0f0',
        }}>
          {footer}
        </footer>
      )}
    </div>
  );
};
```

### 3.3 核心业务逻辑Hook
```typescript
// packages/shared/src/components/business/TableWithView/hooks/useTableWithView.ts
import { useCallback, useReducer, useEffect } from 'react';
import { useEventHandler } from '../../../hooks/useEventHandler';
import { useMappedData } from '../../../context/ComponentDataContext';

export const useTableWithView = (props: TableWithViewProps) => {
  const [state, dispatch] = useReducer(tableWithViewReducer, {
    viewData: [],
    selectedViewKey: '',
    tableData: [],
    loading: false,
  });

  const eventHandler = useEventHandler(props.componentId, props.events);

  // 加载视图数据
  useEffect(() => {
    eventHandler.onMount({
      type: 'loadViews',
      componentId: props.componentId,
      timestamp: Date.now()
    }).then((response: any) => {
      dispatch({ type: 'SET_VIEW_DATA', payload: response?.data || [] });
    });
  }, [eventHandler, props.componentId]);

  // 视图选择处理
  const handleViewSelect = useCallback(async (viewKey: string, viewItem: any) => {
    dispatch({ type: 'SET_LOADING', payload: true });
    dispatch({ type: 'SET_SELECTED_VIEW', payload: viewKey });

    try {
      const response = await eventHandler.onTableDataLoad({
        viewKey,
        searchConditions: viewItem.searchConditions || {},
        componentId: props.componentId,
        timestamp: Date.now()
      });
      
      dispatch({ type: 'SET_TABLE_DATA', payload: response?.data || [] });
    } finally {
      dispatch({ type: 'SET_LOADING', payload: false });
    }
  }, [eventHandler, props.componentId]);

  return {
    viewState: {
      data: state.viewData,
      selectedKey: state.selectedViewKey,
      loading: state.loading,
    },
    tableState: {
      data: state.tableData,
      loading: state.loading,
    },
    handleViewSelect,
    handleTableSearch: (searchValues: any) => {
      // 处理表格搜索
    }
  };
};
```

## 4. 实施步骤

### 4.1 第一步：清理旧代码
1. 删除`SidebarTreeView.tsx`
2. 删除`TableViewWithSearch.tsx`
3. 清理相关的类型定义和元数据

### 4.2 第二步：重构TopDownLayout
1. 简化`TopDownLayout.tsx`
2. 移除所有复杂的组件识别逻辑
3. 采用简单的三段式布局

### 4.3 第三步：创建TableWithView
1. 创建组件目录结构
2. 实现核心类型定义
3. 实现主要的业务逻辑hooks

### 4.4 第四步：实现子组件
1. 实现`ViewPanel`组件
2. 实现`TablePanel`组件
3. 实现主组件`TableWithView`

### 4.5 第五步：更新配置
1. 更新组件导出
2. 更新组件元数据
3. 更新Schema配置文件

### 4.6 第六步：测试验证
1. 单元测试
2. 集成测试
3. 端到端测试

## 5. Schema配置简化

### 5.1 新的Schema结构
```json
{
  "components": [
    {
      "id": "layout",
      "type": "TopDownLayout",
      "props": {
        "headerHeight": 64,
        "footerHeight": 32
      },
      "children": [
        {
          "id": "top_nav",
          "type": "TopNavigation",
          "props": { ... }
        },
        {
          "id": "table_with_view",
          "type": "TableWithView",
          "props": { ... },
          "events": {
            "onMount": {
              "type": "callApi",
              "config": {
                "viewDataApi": { ... },
                "tableDataApi": { ... }
              }
            }
          }
        },
        {
          "id": "status_bar",
          "type": "StatusBar",
          "props": { ... }
        }
      ]
    }
  ]
}
```

### 5.2 配置简化要点
- 移除复杂的组件识别配置
- 简化事件配置结构
- 内置数据联动逻辑

## 6. 预期效果

### 6.1 代码简化
- 减少约40%的代码量
- 移除所有兼容性代码
- 架构更加清晰

### 6.2 维护性提升
- 组件职责更加明确
- 文件组织更加合理
- 测试更加容易

### 6.3 性能优化
- 减少不必要的渲染
- 优化数据加载逻辑
- 提升用户体验

---

**实施原则**：快速、直接、高效。不考虑历史包袱，直接实现最优架构。
