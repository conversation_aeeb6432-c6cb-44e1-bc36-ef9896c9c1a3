# TableWithView技术实现方案

## 1. 组件架构设计

### 1.1 文件结构
```
packages/shared/src/components/business/
├── TableWithView/                 # 组件目录
│   ├── index.tsx                 # 主组件入口
│   ├── TableWithView.tsx         # 主组件实现
│   ├── ViewPanel.tsx             # 左侧视图面板
│   ├── TablePanel.tsx            # 右侧表格面板
│   ├── ViewItem.tsx              # 视图项组件
│   ├── types.ts                  # 类型定义
│   ├── styles.ts                 # 样式定义
│   └── hooks/                    # 自定义hooks
│       ├── useViewData.ts        # 视图数据管理
│       ├── useTableData.ts       # 表格数据管理
│       └── useDataSync.ts        # 数据同步逻辑
└── index.ts                      # 导出文件更新
```

### 1.2 核心类型定义
```typescript
// TableWithView/types.ts
export interface ViewItem {
  key: string;
  title: string;
  icon?: string;
  count?: number;
  children?: ViewItem[];
  searchConditions?: Record<string, any>; // 保存的搜索条件
  disabled?: boolean;
}

export interface ViewPanelProps {
  width?: number;
  searchPlaceholder?: string;
  showSearch?: boolean;
  viewData: ViewItem[];
  selectedViewKey?: string;
  onViewSelect: (viewKey: string, viewItem: ViewItem) => void;
  onViewSearch: (searchValue: string) => void;
}

export interface TablePanelProps extends Omit<TableViewWithSearchProps, 'componentId' | 'events'> {
  selectedView?: ViewItem;
  onDataChange?: (data: any[]) => void;
}
```

## 2. 主组件实现

### 2.1 TableWithView.tsx 核心结构
```typescript
export const TableWithView: React.FC<TableWithViewProps> = ({
  componentId,
  viewConfig = {},
  columns,
  searchFields = [],
  events,
  onViewSelect,
  onTableDataChange,
  style,
  className,
  ...tableProps
}) => {
  // 状态管理
  const [viewData, setViewData] = React.useState<ViewItem[]>([]);
  const [selectedViewKey, setSelectedViewKey] = React.useState<string>('');
  const [tableData, setTableData] = React.useState<any[]>([]);
  const [loading, setLoading] = React.useState(false);

  // 事件处理
  const eventHandler = useEventHandler(componentId, events);
  
  // 自定义hooks
  const { loadViewData } = useViewData(eventHandler);
  const { loadTableData } = useTableData(eventHandler);
  const { syncViewAndTable } = useDataSync();

  // 组件挂载时加载视图数据
  React.useEffect(() => {
    loadViewData().then(setViewData);
  }, [loadViewData]);

  // 视图选择处理
  const handleViewSelect = React.useCallback((viewKey: string, viewItem: ViewItem) => {
    setSelectedViewKey(viewKey);
    setLoading(true);
    
    // 触发视图选择事件
    eventHandler.onViewSelect({
      viewKey,
      viewItem,
      componentId,
      timestamp: Date.now()
    });

    // 根据视图的搜索条件加载表格数据
    loadTableData(viewItem.searchConditions || {})
      .then((data) => {
        setTableData(data);
        onTableDataChange?.(data);
      })
      .finally(() => setLoading(false));

    onViewSelect?.(viewKey, viewItem);
  }, [eventHandler, loadTableData, onTableDataChange, onViewSelect]);

  // 布局样式
  const containerStyle: React.CSSProperties = {
    display: 'flex',
    height: '100%',
    backgroundColor: '#f5f6f8',
    ...style
  };

  return (
    <div className={`lowcode-table-with-view ${className || ''}`} style={containerStyle}>
      {/* 左侧视图面板 */}
      <ViewPanel
        {...viewConfig}
        viewData={viewData}
        selectedViewKey={selectedViewKey}
        onViewSelect={handleViewSelect}
        onViewSearch={(value) => {
          eventHandler.onViewSearch({ searchValue: value, componentId, timestamp: Date.now() });
        }}
      />
      
      {/* 右侧表格面板 */}
      <TablePanel
        {...tableProps}
        columns={columns}
        dataSource={tableData}
        loading={loading}
        searchFields={searchFields}
        selectedView={viewData.find(v => v.key === selectedViewKey)}
        onDataChange={onTableDataChange}
        onSearch={(searchValues) => {
          // 搜索时重新加载数据
          loadTableData(searchValues).then(setTableData);
        }}
      />
    </div>
  );
};
```

### 2.2 ViewPanel子组件
```typescript
// TableWithView/ViewPanel.tsx
export const ViewPanel: React.FC<ViewPanelProps> = ({
  width = 240,
  searchPlaceholder = '搜索视图',
  showSearch = true,
  viewData,
  selectedViewKey,
  onViewSelect,
  onViewSearch
}) => {
  const [searchValue, setSearchValue] = React.useState('');
  const [filteredData, setFilteredData] = React.useState(viewData);

  // 搜索过滤逻辑
  React.useEffect(() => {
    if (!searchValue) {
      setFilteredData(viewData);
      return;
    }

    const filterViews = (items: ViewItem[]): ViewItem[] => {
      return items.reduce((acc: ViewItem[], item) => {
        const matchesSearch = item.title.toLowerCase().includes(searchValue.toLowerCase());
        const filteredChildren = item.children ? filterViews(item.children) : [];

        if (matchesSearch || filteredChildren.length > 0) {
          acc.push({
            ...item,
            children: filteredChildren.length > 0 ? filteredChildren : item.children
          });
        }
        return acc;
      }, []);
    };

    setFilteredData(filterViews(viewData));
  }, [searchValue, viewData]);

  const handleSearch = (value: string) => {
    setSearchValue(value);
    onViewSearch(value);
  };

  const panelStyle: React.CSSProperties = {
    width: `${width}px`,
    flexShrink: 0,
    backgroundColor: '#ffffff',
    borderRight: '1px solid #f0f0f0',
    display: 'flex',
    flexDirection: 'column',
    height: '100%'
  };

  return (
    <div style={panelStyle}>
      {/* 搜索框 */}
      {showSearch && (
        <div style={{ padding: '16px 12px 12px 12px', borderBottom: '1px solid #f0f0f0' }}>
          <input
            type="text"
            placeholder={searchPlaceholder}
            value={searchValue}
            onChange={(e) => handleSearch(e.target.value)}
            style={{
              width: '100%',
              height: '32px',
              padding: '8px 12px',
              border: '1px solid #e0e0e0',
              borderRadius: '16px',
              fontSize: '13px',
              outline: 'none',
              background: '#f8f9fa'
            }}
          />
        </div>
      )}

      {/* 视图列表 */}
      <div style={{ flex: 1, overflow: 'auto', paddingTop: '4px' }}>
        {filteredData.length > 0 ? (
          filteredData.map(item => (
            <ViewItem
              key={item.key}
              item={item}
              selected={selectedViewKey === item.key}
              onSelect={onViewSelect}
            />
          ))
        ) : (
          <div style={{ padding: '40px 20px', textAlign: 'center', color: '#cccccc' }}>
            {searchValue ? '没有找到匹配的结果' : '暂无数据'}
          </div>
        )}
      </div>
    </div>
  );
};
```

## 3. 数据管理Hooks

### 3.1 useViewData Hook
```typescript
// TableWithView/hooks/useViewData.ts
export const useViewData = (eventHandler: any) => {
  const loadViewData = React.useCallback(async (): Promise<ViewItem[]> => {
    try {
      // 触发视图数据加载事件
      const response = await eventHandler.onMount({
        type: 'loadViews',
        timestamp: Date.now()
      });
      
      return response?.data || [];
    } catch (error) {
      console.error('Failed to load view data:', error);
      return [];
    }
  }, [eventHandler]);

  return { loadViewData };
};
```

### 3.2 useTableData Hook
```typescript
// TableWithView/hooks/useTableData.ts
export const useTableData = (eventHandler: any) => {
  const loadTableData = React.useCallback(async (searchConditions: Record<string, any> = {}): Promise<any[]> => {
    try {
      // 触发表格数据加载事件
      const response = await eventHandler.onTableDataLoad({
        searchConditions,
        timestamp: Date.now()
      });
      
      return response?.data || [];
    } catch (error) {
      console.error('Failed to load table data:', error);
      return [];
    }
  }, [eventHandler]);

  return { loadTableData };
};
```

## 4. TopDownLayout重构

### 4.1 简化的布局组件
```typescript
// packages/shared/src/components/layout/TopDownLayout.tsx
export interface TopDownLayoutProps {
  header?: React.ReactNode;
  footer?: React.ReactNode;
  children?: React.ReactNode;
  headerHeight?: number;
  footerHeight?: number;
  style?: React.CSSProperties;
  className?: string;
}

export const TopDownLayout: React.FC<TopDownLayoutProps> = ({
  header,
  footer,
  children,
  headerHeight = 64,
  footerHeight = 32,
  style,
  className,
}) => {
  const layoutStyle: React.CSSProperties = {
    display: 'flex',
    flexDirection: 'column',
    height: '100vh',
    overflow: 'hidden',
    ...style,
  };

  const headerStyle: React.CSSProperties = {
    minHeight: `${headerHeight}px`,
    flexShrink: 0,
    zIndex: 1001,
    backgroundColor: '#fff',
    borderBottom: '1px solid #f0f0f0',
  };

  const mainStyle: React.CSSProperties = {
    flex: 1,
    overflow: 'hidden',
    minHeight: 0,
  };

  const footerStyle: React.CSSProperties = {
    height: `${footerHeight}px`,
    flexShrink: 0,
    zIndex: 999,
    backgroundColor: '#fff',
    borderTop: '1px solid #f0f0f0',
  };

  return (
    <div className={`lowcode-topdown-layout ${className || ''}`} style={layoutStyle}>
      {/* 头部 */}
      {header && (
        <div style={headerStyle}>
          {header}
        </div>
      )}

      {/* 主体区域 */}
      <main style={mainStyle}>
        {children}
      </main>

      {/* 底部 */}
      {footer && (
        <div style={footerStyle}>
          {footer}
        </div>
      )}
    </div>
  );
};
```

## 5. 组件元数据配置

### 5.1 TableWithView元数据
```typescript
// packages/shared/src/components/business/index.ts
export const tableWithViewMeta: ComponentMeta = {
  type: 'TableWithView',
  name: '表格视图',
  description: '集成了视图列表和数据表格的复合组件，支持视图切换和数据联动',
  category: 'business',
  icon: 'table_view',
  props: [
    {
      name: 'viewConfig',
      type: 'object',
      description: '视图面板配置',
      defaultValue: { width: 240, showSearch: true },
      properties: [
        { name: 'width', type: 'number', description: '视图面板宽度' },
        { name: 'showSearch', type: 'boolean', description: '是否显示搜索框' },
        { name: 'searchPlaceholder', type: 'string', description: '搜索框占位符' }
      ]
    },
    {
      name: 'columns',
      type: 'array',
      description: '表格列配置',
      required: true
    },
    {
      name: 'searchFields',
      type: 'array',
      description: '搜索字段配置'
    }
  ],
  events: [
    {
      name: 'onMount',
      description: '组件挂载事件，用于加载视图数据',
      params: [
        { name: 'type', type: 'string', description: '事件类型' },
        { name: 'componentId', type: 'string', description: '组件ID' }
      ]
    },
    {
      name: 'onViewSelect',
      description: '视图选择事件',
      params: [
        { name: 'viewKey', type: 'string', description: '选中的视图key' },
        { name: 'viewItem', type: 'object', description: '选中的视图对象' }
      ]
    },
    {
      name: 'onTableDataLoad',
      description: '表格数据加载事件',
      params: [
        { name: 'searchConditions', type: 'object', description: '搜索条件' }
      ]
    }
  ]
};
```

## 6. 实施计划

### 6.1 开发顺序
1. **第一步**：创建基础类型定义和hooks
2. **第二步**：实现ViewPanel子组件
3. **第三步**：实现TablePanel子组件
4. **第四步**：实现主组件TableWithView
5. **第五步**：调整TopDownLayout布局
6. **第六步**：更新组件元数据和导出
7. **第七步**：更新Schema配置文件

### 6.2 测试策略
1. **单元测试**：每个子组件和hook的独立测试
2. **集成测试**：组件间数据流和事件传递测试
3. **端到端测试**：完整业务流程测试
4. **兼容性测试**：确保不影响现有功能

---

**注意**：实施过程中需要密切关注数据流的正确性和组件间的解耦，确保新组件既能满足业务需求，又保持良好的可维护性。
