import { ComponentMeta } from '../../types';
import { TopNavigation } from './TopNavigation';
import { StatusBar } from './StatusBar';
import { TableWithView } from './TableWithView';

// 导出组件
export { TopNavigation, StatusBar, TableWithView };

// TopNavigation组件元数据
export const topNavigationMeta: ComponentMeta = {
  type: 'TopNavigation',
  name: '顶部导航',
  description: '顶部导航组件，包含Logo、菜单、用户信息等',
  category: 'business',
  icon: 'navigation',
  props: [
    {
      name: 'logo',
      type: 'object',
      description: 'Logo配置，包含src(图片地址)、alt(替代文本)、text(文字)、href(链接)、showDefaultIcon(显示默认图标)、iconText(默认图标文字)等属性',
      default: {
        text: '系统名称',
        href: '/',
        showDefaultIcon: true,
        iconText: '全'
      }
    },
    {
      name: 'menu',
      type: 'array',
      description: '主菜单配置',
      default: [
        { key: 'home', label: '首页', href: '/' },
        { key: 'about', label: '关于' }
      ]
    },

    {
      name: 'user',
      type: 'object',
      description: '用户信息配置',
      default: {
        name: '用户名',
        menu: [
          { key: 'profile', label: '个人资料' },
          { key: 'logout', label: '退出登录' }
        ]
      }
    },
    {
      name: 'actions',
      type: 'array',
      description: '操作按钮配置',
      default: []
    },
    {
      name: 'style',
      type: 'object',
      description: '自定义样式'
    },
    {
      name: 'className',
      type: 'string',
      description: 'CSS类名'
    }
  ],
  events: [
    {
      name: 'onMenuClick',
      description: '菜单点击事件',
      params: [
        { name: 'key', type: 'string', description: '菜单项key' }
      ]
    },
    {
      name: 'onUserMenuClick',
      description: '用户菜单点击事件',
      params: [
        { name: 'key', type: 'string', description: '菜单项key' }
      ]
    }
  ],
  defaultProps: {
    logo: {
      text: '低代码平台',
      href: '/',
      showDefaultIcon: true,
      iconText: '全'
    },
    menu: [
      { key: 'dashboard', label: '仪表板', href: '/dashboard' },
      { key: 'components', label: '组件库', href: '/components' },
      { key: 'templates', label: '模板', href: '/templates' }
    ],
    user: {
      name: '管理员',
      menu: [
        { key: 'profile', label: '个人资料' },
        { key: 'settings', label: '系统设置' },
        { key: 'logout', label: '退出登录' }
      ]
    },
    actions: [
      { key: 'help', label: '帮助', icon: '?' },
      { key: 'notification', label: '通知', icon: '🔔' }
    ]
  }
};





// StatusBar组件元数据
export const statusBarMeta: ComponentMeta = {
  type: 'StatusBar',
  name: '状态栏',
  description: '底部状态栏组件，显示系统状态信息',
  category: 'business',
  icon: 'status',
  props: [
    {
      name: 'items',
      type: 'array',
      required: true,
      description: '状态项配置',
      default: []
    },
    {
      name: 'position',
      type: 'string',
      description: '定位方式',
      default: 'fixed',
      options: [
        { label: '固定定位', value: 'fixed' },
        { label: '相对定位', value: 'relative' }
      ]
    },
    {
      name: 'height',
      type: 'number',
      description: '高度',
      default: 32
    },
    {
      name: 'backgroundColor',
      type: 'string',
      description: '背景颜色',
      default: '#f5f5f5'
    },
    {
      name: 'textColor',
      type: 'string',
      description: '文字颜色',
      default: '#666666'
    },
    {
      name: 'borderTop',
      type: 'string',
      description: '顶部边框',
      default: '1px solid #e8e8e8'
    },
    {
      name: 'showSeparator',
      type: 'boolean',
      description: '显示分隔符',
      default: true
    }
  ],
  events: [
    {
      name: 'onItemClick',
      description: '状态项点击事件',
      params: [
        { name: 'item', type: 'object', description: '点击的状态项' }
      ]
    }
  ],
  defaultProps: {
    items: [
      { key: 'ready', label: '状态', value: '就绪', icon: '✅', color: '#52c41a' },
      { key: 'users', label: '在线用户', value: 128, icon: '👥', clickable: true },
      { key: 'memory', label: '内存使用', value: '45%', icon: '💾' },
      { key: 'time', label: '当前时间', value: new Date().toLocaleTimeString(), icon: '🕐' }
    ],
    position: 'fixed',
    height: 32,
    backgroundColor: '#f5f5f5',
    textColor: '#666666',
    borderTop: '1px solid #e8e8e8',
    showSeparator: true
  }
};

// TableWithView组件元数据
export const tableWithViewMeta: ComponentMeta = {
  type: 'TableWithView',
  name: '表格视图',
  description: '集成了视图列表和数据表格的复合组件，支持视图切换和数据联动，左侧为视图面板，右侧为表格面板',
  category: 'business',
  icon: 'table_view',
  props: [
    {
      name: 'viewConfig',
      type: 'object',
      description: '视图面板配置',
      default: { width: 240, showSearch: true },
      properties: [
        { name: 'width', type: 'number', description: '视图面板宽度', default: 240 },
        { name: 'showSearch', type: 'boolean', description: '是否显示搜索框', default: true },
        { name: 'searchPlaceholder', type: 'string', description: '搜索框占位符', default: '搜索视图' }
      ]
    },
    {
      name: 'columns',
      type: 'array',
      required: true,
      description: '表格列配置',
      default: []
    },
    {
      name: 'title',
      type: 'string',
      description: '表格标题',
      default: '数据表格'
    },
    {
      name: 'searchFields',
      type: 'array',
      description: '搜索字段配置',
      default: []
    },
    {
      name: 'toolbarActions',
      type: 'array',
      description: '工具栏操作按钮',
      default: []
    },
    {
      name: 'quickSearch',
      type: 'object',
      description: '快速搜索配置'
    },
    {
      name: 'groupTabs',
      type: 'array',
      description: '分组标签配置',
      default: []
    },
    {
      name: 'pagination',
      type: 'object',
      description: '分页配置'
    }
  ],
  events: [
    {
      name: 'onMount',
      description: '组件挂载事件，用于加载视图数据和表格数据',
      params: [
        { name: 'type', type: 'string', description: '事件类型' },
        { name: 'componentId', type: 'string', description: '组件ID' }
      ]
    },
    {
      name: 'onViewSelect',
      description: '视图选择事件',
      params: [
        { name: 'viewKey', type: 'string', description: '选中的视图key' },
        { name: 'viewItem', type: 'object', description: '选中的视图对象' }
      ]
    },
    {
      name: 'onTableDataLoad',
      description: '表格数据加载事件',
      params: [
        { name: 'viewKey', type: 'string', description: '视图key' },
        { name: 'searchConditions', type: 'object', description: '搜索条件' }
      ]
    },
    {
      name: 'onSearch',
      description: '表格搜索事件',
      params: [
        { name: 'searchValues', type: 'object', description: '搜索值对象' }
      ]
    }
  ],
  defaultProps: {
    viewConfig: {
      width: 240,
      showSearch: true,
      searchPlaceholder: '搜索视图'
    },
    title: '数据表格',
    columns: [
      { key: 'id', title: 'ID', dataIndex: 'id', width: '10%' },
      { key: 'name', title: '名称', dataIndex: 'name', width: '30%' },
      { key: 'status', title: '状态', dataIndex: 'status', width: '20%' },
      { key: 'createTime', title: '创建时间', dataIndex: 'createTime', width: '40%' }
    ],
    searchFields: [
      { key: 'name', label: '名称', type: 'input', placeholder: '请输入名称' },
      { key: 'status', label: '状态', type: 'select', placeholder: '请选择状态', options: [
        { label: '启用', value: 'enabled' },
        { label: '禁用', value: 'disabled' }
      ]}
    ],
    toolbarActions: [
      { key: 'add', label: '新增', type: 'primary' },
      { key: 'export', label: '导出', type: 'default' }
    ],
    quickSearch: {
      placeholder: '快速搜索'
    }
  }
};

// 业务组件元数据集合
export const businessComponentMetas: ComponentMeta[] = [
  topNavigationMeta,
  statusBarMeta,
  tableWithViewMeta
];

// 业务组件映射
export const businessComponents = {
  TopNavigation,
  StatusBar,
  TableWithView
};
