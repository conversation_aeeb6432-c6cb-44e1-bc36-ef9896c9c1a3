import React from 'react';
import { ViewPanel } from './components/ViewPanel';
import { TablePanel } from './components/TablePanel';
import { useTableWithView } from './hooks/useTableWithView';
import { TableWithViewProps } from './types';
import { CLASS_NAMES, DEFAULT_VIEW_WIDTH } from './constants';

export const TableWithView: React.FC<TableWithViewProps> = (props) => {
  const {
    componentId,
    viewConfig = {},
    columns,
    searchFields = [],
    toolbarActions = [],
    toolbarIcons = [],
    quickSearch,
    groupTabs = [],
    showCancelGroup = false,
    rowActions = [],
    pagination,
    rowSelection,
    showMoreSearchConditions = false,
    title,
    events,
    style,
    className,
    onViewSelect,
    onSearch,
    onTableChange,
    onGroupTabChange,
    onCancelGroup,
    onMoreSearchConditions,
    ...rest
  } = props;

  // 过滤掉事件处理器，避免传递到DOM元素
  const {
    onMount,
    onTableDataLoad,
    ...domProps
  } = rest as any;

  // 使用核心业务逻辑hook
  const {
    viewState,
    tableState,
    handleViewSelect,
    handleTableSearch,
    handleViewSearch,
    loading
  } = useTableWithView(props);

  // 容器样式
  const containerStyle: React.CSSProperties = {
    display: 'flex',
    height: '100%',
    backgroundColor: '#f5f6f8',
    ...style,
  };

  // 视图选择处理
  const onViewSelectHandler = (viewKey: string, viewItem: any) => {
    handleViewSelect(viewKey, viewItem);
    onViewSelect?.(viewKey, viewItem);
  };

  // 表格搜索处理
  const onTableSearchHandler = (searchValues: Record<string, any>) => {
    handleTableSearch(searchValues);
    onSearch?.(searchValues);
  };

  return (
    <div 
      className={`${CLASS_NAMES.CONTAINER} ${className || ''}`} 
      style={containerStyle}
      {...domProps}
    >
      {/* 左侧视图面板 */}
      <ViewPanel
        data={viewState.data}
        selectedKey={viewState.selectedKey}
        loading={viewState.loading}
        width={viewConfig.width || DEFAULT_VIEW_WIDTH}
        searchPlaceholder={viewConfig.searchPlaceholder}
        showSearch={viewConfig.showSearch}
        onSelect={onViewSelectHandler}
        onSearch={handleViewSearch}
      />
      
      {/* 右侧表格面板 */}
      <TablePanel
        data={tableState.data}
        loading={tableState.loading}
        columns={columns}
        searchFields={searchFields}
        toolbarActions={toolbarActions}
        toolbarIcons={toolbarIcons}
        quickSearch={quickSearch}
        groupTabs={groupTabs}
        showCancelGroup={showCancelGroup}
        rowActions={rowActions}
        pagination={pagination}
        rowSelection={rowSelection}
        showMoreSearchConditions={showMoreSearchConditions}
        title={title}
        selectedView={viewState.data.find(v => v.key === viewState.selectedKey)}
        onSearch={onTableSearchHandler}
        onTableChange={onTableChange}
        onGroupTabChange={onGroupTabChange}
        onCancelGroup={onCancelGroup}
        onMoreSearchConditions={onMoreSearchConditions}
      />
    </div>
  );
};
