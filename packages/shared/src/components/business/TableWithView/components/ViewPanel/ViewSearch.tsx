import React from 'react';

interface ViewSearchProps {
  value: string;
  placeholder?: string;
  onChange: (value: string) => void;
}

export const ViewSearch: React.FC<ViewSearchProps> = ({
  value,
  placeholder = '搜索视图',
  onChange
}) => {
  const containerStyle: React.CSSProperties = {
    padding: '16px 12px 12px 12px',
    borderBottom: '1px solid #f0f0f0'
  };

  const inputWrapperStyle: React.CSSProperties = {
    position: 'relative'
  };

  const inputStyle: React.CSSProperties = {
    width: '100%',
    height: '32px',
    padding: '8px 36px 8px 12px',
    border: '1px solid #e0e0e0',
    borderRadius: '16px',
    fontSize: '13px',
    outline: 'none',
    background: '#f8f9fa',
    transition: 'all 0.3s ease',
    color: '#333333',
    boxSizing: 'border-box'
  };

  const iconStyle: React.CSSProperties = {
    position: 'absolute',
    right: '12px',
    top: '50%',
    transform: 'translateY(-50%)',
    fontSize: '16px',
    color: '#999999',
    pointerEvents: 'none'
  };

  const clearIconStyle: React.CSSProperties = {
    position: 'absolute',
    right: '12px',
    top: '50%',
    transform: 'translateY(-50%)',
    fontSize: '14px',
    color: '#999999',
    cursor: 'pointer',
    padding: '2px',
    borderRadius: '50%',
    backgroundColor: 'transparent',
    transition: 'background-color 0.2s ease'
  };

  const [isFocused, setIsFocused] = React.useState(false);

  const focusedInputStyle: React.CSSProperties = {
    borderColor: '#1890ff',
    background: '#ffffff',
    boxShadow: '0 0 0 2px rgba(24, 144, 255, 0.2)'
  };

  const handleClear = () => {
    onChange('');
  };

  return (
    <div style={containerStyle}>
      <div style={inputWrapperStyle}>
        <input
          type="text"
          placeholder={placeholder}
          value={value}
          onChange={(e) => onChange(e.target.value)}
          onFocus={() => setIsFocused(true)}
          onBlur={() => setIsFocused(false)}
          style={{
            ...inputStyle,
            ...(isFocused ? focusedInputStyle : {})
          }}
        />
        {value ? (
          <span
            style={clearIconStyle}
            onClick={handleClear}
            onMouseEnter={(e) => {
              (e.target as HTMLElement).style.backgroundColor = '#f0f0f0';
            }}
            onMouseLeave={(e) => {
              (e.target as HTMLElement).style.backgroundColor = 'transparent';
            }}
          >
            ✕
          </span>
        ) : (
          <span style={iconStyle}>
            🔍
          </span>
        )}
      </div>
    </div>
  );
};
