import React from 'react';
import { ViewPanelProps, ViewItem as ViewItemType } from '../../types';
import { ViewItem } from './ViewItem';
import { ViewSearch } from './ViewSearch';
import { DEFAULT_VIEW_WIDTH, DEFAULT_SEARCH_PLACEHOLDER, CLASS_NAMES } from '../../constants';

export const ViewPanel: React.FC<ViewPanelProps> = ({
  data,
  selectedKey,
  loading = false,
  width = DEFAULT_VIEW_WIDTH,
  searchPlaceholder = DEFAULT_SEARCH_PLACEHOLDER,
  showSearch = true,
  onSelect,
  onSearch
}) => {
  const [searchValue, setSearchValue] = React.useState('');
  const [filteredData, setFilteredData] = React.useState(data);
  const [expandedKeys, setExpandedKeys] = React.useState<string[]>([]);

  // 搜索过滤逻辑
  React.useEffect(() => {
    if (!searchValue) {
      setFilteredData(data);
      return;
    }

    const filterViews = (items: ViewItemType[]): ViewItemType[] => {
      return items.reduce((acc: ViewItemType[], item) => {
        const matchesSearch = item.title.toLowerCase().includes(searchValue.toLowerCase());
        const filteredChildren = item.children ? filterViews(item.children) : [];

        if (matchesSearch || filteredChildren.length > 0) {
          acc.push({
            ...item,
            children: filteredChildren.length > 0 ? filteredChildren : item.children
          });
        }
        return acc;
      }, []);
    };

    const filtered = filterViews(data);
    setFilteredData(filtered);

    // 搜索时自动展开所有匹配的节点
    if (searchValue) {
      const getAllKeys = (items: ViewItemType[]): string[] => {
        let keys: string[] = [];
        items.forEach(item => {
          keys.push(item.key);
          if (item.children) {
            keys = keys.concat(getAllKeys(item.children));
          }
        });
        return keys;
      };
      setExpandedKeys(getAllKeys(filtered));
    }
  }, [searchValue, data]);

  // 当数据变化时，设置默认展开状态
  React.useEffect(() => {
    if (data.length > 0 && expandedKeys.length === 0) {
      const getDefaultExpandedKeys = (items: ViewItemType[]): string[] => {
        return items
          .filter(item => item.children && item.children.length > 0)
          .map(item => item.key);
      };
      setExpandedKeys(getDefaultExpandedKeys(data));
    }
  }, [data]);

  const handleSearch = (value: string) => {
    setSearchValue(value);
    onSearch?.(value);
  };

  const handleToggle = (viewKey: string) => {
    setExpandedKeys(prev => 
      prev.includes(viewKey) 
        ? prev.filter(key => key !== viewKey)
        : [...prev, viewKey]
    );
  };

  const panelStyle: React.CSSProperties = {
    width: `${width}px`,
    flexShrink: 0,
    backgroundColor: '#ffffff',
    borderRight: '1px solid #f0f0f0',
    display: 'flex',
    flexDirection: 'column',
    height: '100%',
    position: 'relative'
  };

  const contentStyle: React.CSSProperties = {
    flex: 1,
    overflow: 'auto',
    paddingTop: '4px'
  };

  const emptyStyle: React.CSSProperties = {
    padding: '40px 20px',
    textAlign: 'center',
    color: '#cccccc',
    fontSize: '14px',
    display: 'flex',
    flexDirection: 'column',
    alignItems: 'center',
    gap: '8px'
  };

  const loadingStyle: React.CSSProperties = {
    padding: '40px 20px',
    textAlign: 'center',
    color: '#999999',
    fontSize: '14px'
  };

  const renderViewItem = (item: ViewItemType) => (
    <ViewItem
      key={item.key}
      item={item}
      selected={selectedKey === item.key}
      onSelect={onSelect}
      onToggle={handleToggle}
      expanded={expandedKeys.includes(item.key)}
    />
  );

  return (
    <div className={CLASS_NAMES.VIEW_PANEL} style={panelStyle}>
      {/* 搜索框 */}
      {showSearch && (
        <ViewSearch
          value={searchValue}
          placeholder={searchPlaceholder}
          onChange={handleSearch}
        />
      )}

      {/* 视图列表 */}
      <div style={contentStyle}>
        {loading ? (
          <div style={loadingStyle}>
            <span>加载中...</span>
          </div>
        ) : filteredData.length > 0 ? (
          filteredData.map(renderViewItem)
        ) : (
          <div style={emptyStyle}>
            <span style={{ fontSize: '32px', opacity: 0.5 }}>
              {searchValue ? '🔍' : '📁'}
            </span>
            <span>
              {searchValue ? '没有找到匹配的结果' : '暂无数据'}
            </span>
          </div>
        )}
      </div>
    </div>
  );
};
