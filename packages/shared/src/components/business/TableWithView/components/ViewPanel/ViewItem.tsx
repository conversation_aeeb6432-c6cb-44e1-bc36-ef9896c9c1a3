import React from 'react';
import { ViewItem as ViewItemType } from '../../types';

interface ViewItemProps {
  item: ViewItemType;
  selected: boolean;
  level?: number;
  onSelect: (viewKey: string, viewItem: ViewItemType) => void;
  onToggle?: (viewKey: string) => void;
  expanded?: boolean;
}

export const ViewItem: React.FC<ViewItemProps> = ({
  item,
  selected,
  level = 0,
  onSelect,
  onToggle,
  expanded = false
}) => {
  const handleClick = () => {
    if (item.disabled) return;
    onSelect(item.key, item);
  };

  const handleToggle = (e: React.MouseEvent) => {
    e.stopPropagation();
    onToggle?.(item.key);
  };

  const itemStyle: React.CSSProperties = {
    padding: `8px 12px 8px ${12 + level * 20}px`,
    cursor: item.disabled ? 'not-allowed' : 'pointer',
    backgroundColor: selected ? '#e6f7ff' : 'transparent',
    borderLeft: selected ? '3px solid #1890ff' : '3px solid transparent',
    display: 'flex',
    alignItems: 'center',
    justifyContent: 'space-between',
    fontSize: '14px',
    color: item.disabled ? '#cccccc' : '#333333',
    transition: 'all 0.2s ease',
    borderBottom: '1px solid #f5f5f5',
  };

  const hoverStyle: React.CSSProperties = {
    backgroundColor: selected ? '#e6f7ff' : '#f5f5f5',
  };

  const [isHovered, setIsHovered] = React.useState(false);

  const titleStyle: React.CSSProperties = {
    flex: 1,
    display: 'flex',
    alignItems: 'center',
    gap: '8px',
  };

  const countStyle: React.CSSProperties = {
    fontSize: '12px',
    color: '#999999',
    backgroundColor: '#f0f0f0',
    padding: '2px 6px',
    borderRadius: '10px',
    minWidth: '20px',
    textAlign: 'center',
  };

  const expandIconStyle: React.CSSProperties = {
    fontSize: '12px',
    color: '#999999',
    transition: 'transform 0.2s ease',
    transform: expanded ? 'rotate(90deg)' : 'rotate(0deg)',
    marginRight: '4px',
  };

  return (
    <>
      <div
        style={{
          ...itemStyle,
          ...(isHovered ? hoverStyle : {})
        }}
        onClick={handleClick}
        onMouseEnter={() => setIsHovered(true)}
        onMouseLeave={() => setIsHovered(false)}
      >
        <div style={titleStyle}>
          {item.children && item.children.length > 0 && (
            <span style={expandIconStyle} onClick={handleToggle}>
              ▶
            </span>
          )}
          {item.icon && (
            <span style={{ fontSize: '16px' }}>
              {item.icon}
            </span>
          )}
          <span style={{ 
            fontWeight: selected ? 'bold' : 'normal',
            overflow: 'hidden',
            textOverflow: 'ellipsis',
            whiteSpace: 'nowrap'
          }}>
            {item.title}
          </span>
        </div>
        
        {typeof item.count === 'number' && (
          <span style={countStyle}>
            {item.count}
          </span>
        )}
      </div>

      {/* 子项 */}
      {item.children && item.children.length > 0 && expanded && (
        <div>
          {item.children.map(child => (
            <ViewItem
              key={child.key}
              item={child}
              selected={selected}
              level={level + 1}
              onSelect={onSelect}
              onToggle={onToggle}
              expanded={expanded}
            />
          ))}
        </div>
      )}
    </>
  );
};
