import React from 'react';
import { TablePanelProps } from '../../types';
import { CLASS_NAMES } from '../../constants';

export const TablePanel: React.FC<TablePanelProps> = ({
  data = [],
  loading = false,
  columns,
  searchFields = [],
  toolbarActions = [],
  toolbarIcons = [],
  quickSearch,
  groupTabs = [],
  showCancelGroup = false,
  rowActions = [],
  pagination,
  rowSelection,
  showMoreSearchConditions = false,
  title = '数据表格',
  selectedView,
  onSearch,
  onTableChange,
  onGroupTabChange,
  onCancelGroup,
  onMoreSearchConditions
}) => {
  const [searchValues, setSearchValues] = React.useState<Record<string, any>>({});
  const [selectedRowKeys, setSelectedRowKeys] = React.useState<string[]>(
    rowSelection?.selectedRowKeys || []
  );
  const [sortConfig, setSortConfig] = React.useState<{
    key: string;
    direction: 'asc' | 'desc';
  } | null>(null);
  const [quickSearchValue, setQuickSearchValue] = React.useState('');

  // 处理搜索
  const handleSearch = () => {
    onSearch?.(searchValues);
  };

  // 重置搜索
  const handleReset = () => {
    setSearchValues({});
    onSearch?.({});
  };

  // 处理搜索字段变化
  const handleSearchFieldChange = (key: string, value: any) => {
    const newValues = { ...searchValues, [key]: value };
    setSearchValues(newValues);
  };

  // 处理行选择
  const handleRowSelection = (keys: string[], rows: any[]) => {
    setSelectedRowKeys(keys);
    rowSelection?.onChange?.(keys, rows);
  };

  // 处理排序
  const handleSort = (columnKey: string) => {
    const newDirection = 
      sortConfig?.key === columnKey && sortConfig.direction === 'asc' 
        ? 'desc' 
        : 'asc';
    
    setSortConfig({ key: columnKey, direction: newDirection });
    
    // 触发表格变化事件
    onTableChange?.(pagination, {}, { field: columnKey, order: newDirection });
  };

  // 渲染工具栏
  const renderTopToolbar = () => (
    <div style={{
      display: 'flex',
      justifyContent: 'space-between',
      alignItems: 'center',
      padding: '16px 0 12px 0',
      borderBottom: '1px solid #f0f0f0',
      marginBottom: '16px'
    }}>
      <div style={{ display: 'flex', alignItems: 'center', gap: '16px' }}>
        <h3 style={{ margin: 0, fontSize: '16px', fontWeight: 'bold' }}>
          {selectedView?.title || title}
        </h3>
        {quickSearch && (
          <div style={{ position: 'relative' }}>
            <input
              type="text"
              placeholder={quickSearch.placeholder || '快速搜索'}
              value={quickSearchValue}
              onChange={(e) => setQuickSearchValue(e.target.value)}
              style={{
                width: '240px',
                height: '32px',
                padding: '8px 36px 8px 12px',
                border: '1px solid #e0e0e0',
                borderRadius: '6px',
                fontSize: '14px',
                outline: 'none'
              }}
            />
            <span style={{
              position: 'absolute',
              right: '12px',
              top: '50%',
              transform: 'translateY(-50%)',
              color: '#999999'
            }}>
              🔍
            </span>
          </div>
        )}
      </div>
      
      <div style={{ display: 'flex', alignItems: 'center', gap: '8px' }}>
        {toolbarActions.map(action => (
          <button
            key={action.key}
            onClick={action.onClick}
            disabled={action.disabled}
            style={{
              padding: '6px 12px',
              border: action.type === 'primary' ? 'none' : '1px solid #e0e0e0',
              borderRadius: '4px',
              backgroundColor: action.type === 'primary' ? '#1890ff' : '#ffffff',
              color: action.type === 'primary' ? '#ffffff' : '#333333',
              cursor: action.disabled ? 'not-allowed' : 'pointer',
              fontSize: '14px'
            }}
          >
            {action.icon && <span style={{ marginRight: '4px' }}>{action.icon}</span>}
            {action.label}
          </button>
        ))}
        
        {toolbarIcons.map(icon => (
          <button
            key={icon.key}
            onClick={icon.onClick}
            title={icon.tooltip}
            style={{
              padding: '6px',
              border: '1px solid #e0e0e0',
              borderRadius: '4px',
              backgroundColor: '#ffffff',
              cursor: 'pointer',
              fontSize: '16px'
            }}
          >
            {icon.icon}
          </button>
        ))}
      </div>
    </div>
  );

  // 渲染搜索表单
  const renderSearchForm = () => {
    if (searchFields.length === 0) return null;

    return (
      <div style={{
        padding: '16px',
        backgroundColor: '#fafafa',
        borderRadius: '6px',
        marginBottom: '16px'
      }}>
        <div style={{
          display: 'grid',
          gridTemplateColumns: 'repeat(auto-fit, minmax(200px, 1fr))',
          gap: '16px',
          marginBottom: '16px'
        }}>
          {searchFields.map(field => (
            <div key={field.key}>
              <label style={{
                display: 'block',
                marginBottom: '4px',
                fontSize: '14px',
                color: '#333333'
              }}>
                {field.label}
              </label>
              {field.type === 'input' && (
                <input
                  type="text"
                  placeholder={field.placeholder}
                  value={searchValues[field.key] || ''}
                  onChange={(e) => handleSearchFieldChange(field.key, e.target.value)}
                  style={{
                    width: '100%',
                    height: '32px',
                    padding: '8px 12px',
                    border: '1px solid #e0e0e0',
                    borderRadius: '4px',
                    fontSize: '14px',
                    outline: 'none'
                  }}
                />
              )}
              {field.type === 'select' && (
                <select
                  value={searchValues[field.key] || ''}
                  onChange={(e) => handleSearchFieldChange(field.key, e.target.value)}
                  style={{
                    width: '100%',
                    height: '32px',
                    padding: '8px 12px',
                    border: '1px solid #e0e0e0',
                    borderRadius: '4px',
                    fontSize: '14px',
                    outline: 'none'
                  }}
                >
                  <option value="">{field.placeholder}</option>
                  {field.options?.map(option => (
                    <option key={option.value} value={option.value}>
                      {option.label}
                    </option>
                  ))}
                </select>
              )}
            </div>
          ))}
        </div>
        
        <div style={{ display: 'flex', gap: '8px' }}>
          <button
            onClick={handleSearch}
            style={{
              padding: '6px 16px',
              border: 'none',
              borderRadius: '4px',
              backgroundColor: '#1890ff',
              color: '#ffffff',
              cursor: 'pointer',
              fontSize: '14px'
            }}
          >
            搜索
          </button>
          <button
            onClick={handleReset}
            style={{
              padding: '6px 16px',
              border: '1px solid #e0e0e0',
              borderRadius: '4px',
              backgroundColor: '#ffffff',
              color: '#333333',
              cursor: 'pointer',
              fontSize: '14px'
            }}
          >
            重置
          </button>
          {showMoreSearchConditions && (
            <button
              onClick={onMoreSearchConditions}
              style={{
                padding: '6px 16px',
                border: '1px solid #e0e0e0',
                borderRadius: '4px',
                backgroundColor: '#ffffff',
                color: '#1890ff',
                cursor: 'pointer',
                fontSize: '14px'
              }}
            >
              更多条件
            </button>
          )}
        </div>
      </div>
    );
  };

  // 渲染分组标签
  const renderGroupTabs = () => {
    if (groupTabs.length === 0) return null;

    return (
      <div style={{
        display: 'flex',
        alignItems: 'center',
        gap: '8px',
        marginBottom: '16px',
        padding: '0 0 12px 0',
        borderBottom: '1px solid #f0f0f0'
      }}>
        {groupTabs.map(tab => (
          <button
            key={tab.key}
            onClick={() => onGroupTabChange?.(tab.key)}
            style={{
              padding: '6px 12px',
              border: tab.active ? 'none' : '1px solid #e0e0e0',
              borderRadius: '4px',
              backgroundColor: tab.active ? '#1890ff' : '#ffffff',
              color: tab.active ? '#ffffff' : '#333333',
              cursor: 'pointer',
              fontSize: '14px',
              display: 'flex',
              alignItems: 'center',
              gap: '4px'
            }}
          >
            {tab.label}
            {typeof tab.count === 'number' && (
              <span style={{
                backgroundColor: tab.active ? 'rgba(255,255,255,0.2)' : '#f0f0f0',
                padding: '2px 6px',
                borderRadius: '10px',
                fontSize: '12px'
              }}>
                {tab.count}
              </span>
            )}
          </button>
        ))}
        
        {showCancelGroup && (
          <button
            onClick={onCancelGroup}
            style={{
              padding: '6px 12px',
              border: '1px solid #e0e0e0',
              borderRadius: '4px',
              backgroundColor: '#ffffff',
              color: '#999999',
              cursor: 'pointer',
              fontSize: '14px'
            }}
          >
            取消分组
          </button>
        )}
      </div>
    );
  };

  const panelStyle: React.CSSProperties = {
    flex: 1,
    backgroundColor: '#ffffff',
    padding: '16px',
    overflow: 'hidden',
    display: 'flex',
    flexDirection: 'column'
  };

  return (
    <div className={CLASS_NAMES.TABLE_PANEL} style={panelStyle}>
      {renderTopToolbar()}
      {renderSearchForm()}
      {renderGroupTabs()}
      
      {/* 表格区域 - 简化版本，后续可以扩展 */}
      <div style={{
        flex: 1,
        overflow: 'auto',
        border: '1px solid #f0f0f0',
        borderRadius: '6px'
      }}>
        {loading ? (
          <div style={{
            padding: '40px',
            textAlign: 'center',
            color: '#999999'
          }}>
            加载中...
          </div>
        ) : data.length > 0 ? (
          <div style={{ padding: '20px', textAlign: 'center' }}>
            表格数据展示区域 (共 {data.length} 条数据)
            <br />
            <small style={{ color: '#999999' }}>
              完整的表格实现将在下一步完成
            </small>
          </div>
        ) : (
          <div style={{
            padding: '40px',
            textAlign: 'center',
            color: '#cccccc'
          }}>
            暂无数据
          </div>
        )}
      </div>
    </div>
  );
};
