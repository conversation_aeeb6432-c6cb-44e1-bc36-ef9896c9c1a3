// 默认配置常量
export const DEFAULT_VIEW_WIDTH = 240;
export const DEFAULT_SEARCH_PLACEHOLDER = '搜索视图';
export const CACHE_DURATION = 5 * 60 * 1000; // 5分钟缓存
export const VIRTUAL_SCROLL_THRESHOLD = 1000; // 虚拟滚动阈值

// 组件类名常量
export const CLASS_NAMES = {
  CONTAINER: 'lowcode-table-with-view',
  VIEW_PANEL: 'lowcode-view-panel',
  TABLE_PANEL: 'lowcode-table-panel',
  VIEW_ITEM: 'lowcode-view-item',
  VIEW_SEARCH: 'lowcode-view-search',
} as const;

// 事件类型常量
export const EVENT_TYPES = {
  MOUNT: 'onMount',
  VIEW_SELECT: 'onViewSelect',
  TABLE_DATA_LOAD: 'onTableDataLoad',
  SEARCH: 'onSearch',
  VIEW_SEARCH: 'onViewSearch',
} as const;

// Reducer Action类型常量
export const ACTION_TYPES = {
  SET_VIEW_DATA: 'SET_VIEW_DATA',
  SET_SELECTED_VIEW: 'SET_SELECTED_VIEW',
  SET_TABLE_DATA: 'SET_TABLE_DATA',
  SET_LOADING: 'SET_LOADING',
  SET_VIEW_LOADING: 'SET_VIEW_LOADING',
  SET_TABLE_LOADING: 'SET_TABLE_LOADING',
} as const;
