import React from 'react';

// 视图项接口
export interface ViewItem {
  key: string;
  title: string;
  icon?: string;
  count?: number;
  children?: ViewItem[];
  searchConditions?: Record<string, any>; // 保存的搜索条件
  disabled?: boolean;
  href?: string;
}

// 表格列接口
export interface TableColumn {
  key: string;
  title: string;
  dataIndex: string;
  width?: number | string;
  align?: 'left' | 'center' | 'right';
  sortable?: boolean;
  filterable?: boolean;
  render?: (value: any, record: any, index: number) => React.ReactNode;
}

// 搜索字段接口
export interface SearchField {
  key: string;
  label: string;
  type: 'input' | 'select' | 'combinedSelect' | 'checkbox' | 'dateRange';
  placeholder?: string;
  options?: Array<{ label: string; value: string }>;
  combinedConfig?: {
    leftOptions: Array<{ label: string; value: string }>;
    rightOptions: Array<{ label: string; value: string }>;
  };
  checkboxConfig?: {
    text: string;
    tooltip?: string;
  };
}

// 工具栏操作接口
export interface ToolbarAction {
  key: string;
  label: string;
  icon?: string;
  type?: 'primary' | 'default' | 'danger';
  disabled?: boolean;
  onClick?: () => void;
}

// 工具栏图标接口
export interface ToolbarIcon {
  key: string;
  icon: string;
  tooltip?: string;
  onClick?: () => void;
}

// 分组标签接口
export interface GroupTab {
  key: string;
  label: string;
  count?: number;
  active?: boolean;
}

// 行操作接口
export interface TableAction {
  key: string;
  label: string;
  icon?: string;
  type?: 'primary' | 'default' | 'danger';
  disabled?: boolean;
  onClick?: (record: any, index: number) => void;
}

// 分页配置接口
export interface PaginationConfig {
  current: number;
  pageSize: number;
  total: number;
  showSizeChanger?: boolean;
  showQuickJumper?: boolean;
  showTotal?: boolean;
}

// 行选择配置接口
export interface RowSelectionConfig {
  type: 'checkbox' | 'radio';
  selectedRowKeys?: string[];
  onChange?: (selectedRowKeys: string[], selectedRows: any[]) => void;
}

// 快速搜索配置接口
export interface QuickSearchConfig {
  placeholder?: string;
  onSearch?: (value: string) => void;
}

// 视图配置接口
export interface ViewConfig {
  width?: number;
  searchPlaceholder?: string;
  showSearch?: boolean;
}

// 视图面板Props接口
export interface ViewPanelProps {
  data: ViewItem[];
  selectedKey?: string;
  loading?: boolean;
  width?: number;
  searchPlaceholder?: string;
  showSearch?: boolean;
  onSelect: (viewKey: string, viewItem: ViewItem) => void;
  onSearch?: (searchValue: string) => void;
}

// 表格面板Props接口
export interface TablePanelProps {
  data: any[];
  loading?: boolean;
  columns: TableColumn[];
  searchFields?: SearchField[];
  toolbarActions?: ToolbarAction[];
  toolbarIcons?: ToolbarIcon[];
  quickSearch?: QuickSearchConfig;
  groupTabs?: GroupTab[];
  showCancelGroup?: boolean;
  rowActions?: TableAction[];
  pagination?: PaginationConfig;
  rowSelection?: RowSelectionConfig;
  showMoreSearchConditions?: boolean;
  title?: string;
  selectedView?: ViewItem;
  onSearch?: (searchValues: Record<string, any>) => void;
  onTableChange?: (pagination: any, filters: any, sorter: any) => void;
  onGroupTabChange?: (tabKey: string) => void;
  onCancelGroup?: () => void;
  onMoreSearchConditions?: () => void;
}

// TableWithView主组件Props接口
export interface TableWithViewProps extends React.HTMLAttributes<HTMLDivElement> {
  // 基础配置
  componentId?: string;
  
  // 视图配置
  viewConfig?: ViewConfig;
  
  // 表格配置
  title?: string;
  columns: TableColumn[];
  searchFields?: SearchField[];
  toolbarActions?: ToolbarAction[];
  toolbarIcons?: ToolbarIcon[];
  quickSearch?: QuickSearchConfig;
  groupTabs?: GroupTab[];
  showCancelGroup?: boolean;
  rowActions?: TableAction[];
  pagination?: PaginationConfig;
  rowSelection?: RowSelectionConfig;
  showMoreSearchConditions?: boolean;
  
  // 事件配置
  events?: Record<string, any>;
  
  // 事件处理器（不会传递到DOM）
  onMount?: (data?: any) => void;
  onViewSelect?: (viewKey: string, viewData: ViewItem) => void;
  onTableDataLoad?: (data?: any) => void;
  onSearch?: (searchValues: Record<string, any>) => void;
  onTableChange?: (pagination: any, filters: any, sorter: any) => void;
  onGroupTabChange?: (tabKey: string) => void;
  onCancelGroup?: () => void;
  onMoreSearchConditions?: () => void;
  
  // 样式配置
  style?: React.CSSProperties;
  className?: string;
}

// 组件状态接口
export interface TableWithViewState {
  viewData: ViewItem[];
  selectedViewKey: string;
  tableData: any[];
  loading: boolean;
  viewLoading: boolean;
  tableLoading: boolean;
}

// Reducer Action接口
export interface TableWithViewAction {
  type: 'SET_VIEW_DATA' | 'SET_SELECTED_VIEW' | 'SET_TABLE_DATA' | 'SET_LOADING' | 'SET_VIEW_LOADING' | 'SET_TABLE_LOADING';
  payload: any;
}

// 数据缓存项接口
export interface CacheItem {
  data: any;
  timestamp: number;
}
