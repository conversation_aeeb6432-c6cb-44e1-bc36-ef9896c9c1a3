import { useRef, useCallback } from 'react';
import { CacheItem } from '../types';
import { CACHE_DURATION } from '../constants';

export const useDataCache = () => {
  const cache = useRef(new Map<string, CacheItem>());

  // 获取缓存数据
  const get = useCallback((key: string) => {
    const item = cache.current.get(key);
    if (item && Date.now() - item.timestamp < CACHE_DURATION) {
      return item.data;
    }
    // 缓存过期，删除该项
    if (item) {
      cache.current.delete(key);
    }
    return null;
  }, []);

  // 设置缓存数据
  const set = useCallback((key: string, data: any) => {
    cache.current.set(key, {
      data,
      timestamp: Date.now()
    });
  }, []);

  // 清除所有缓存
  const clear = useCallback(() => {
    cache.current.clear();
  }, []);

  // 清除指定缓存
  const remove = useCallback((key: string) => {
    cache.current.delete(key);
  }, []);

  // 获取缓存大小
  const size = useCallback(() => {
    return cache.current.size;
  }, []);

  // 检查是否有缓存
  const has = useCallback((key: string) => {
    const item = cache.current.get(key);
    return item && Date.now() - item.timestamp < CACHE_DURATION;
  }, []);

  return {
    get,
    set,
    clear,
    remove,
    size,
    has
  };
};
