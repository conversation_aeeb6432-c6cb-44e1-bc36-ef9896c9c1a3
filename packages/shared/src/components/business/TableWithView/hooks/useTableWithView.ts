import { useCallback, useReducer, useEffect } from 'react';
import { useEventHandler } from '../../../hooks/useEventHandler';
import { TableWithViewProps, TableWithViewState, TableWithViewAction, ViewItem } from '../types';
import { ACTION_TYPES, EVENT_TYPES } from '../constants';

// 初始状态
const initialState: TableWithViewState = {
  viewData: [],
  selectedViewKey: '',
  tableData: [],
  loading: false,
  viewLoading: false,
  tableLoading: false,
};

// Reducer函数
const tableWithViewReducer = (state: TableWithViewState, action: TableWithViewAction): TableWithViewState => {
  switch (action.type) {
    case ACTION_TYPES.SET_VIEW_DATA:
      return { ...state, viewData: action.payload };
    case ACTION_TYPES.SET_SELECTED_VIEW:
      return { ...state, selectedViewKey: action.payload };
    case ACTION_TYPES.SET_TABLE_DATA:
      return { ...state, tableData: action.payload };
    case ACTION_TYPES.SET_LOADING:
      return { ...state, loading: action.payload };
    case ACTION_TYPES.SET_VIEW_LOADING:
      return { ...state, viewLoading: action.payload };
    case ACTION_TYPES.SET_TABLE_LOADING:
      return { ...state, tableLoading: action.payload };
    default:
      return state;
  }
};

export const useTableWithView = (props: TableWithViewProps) => {
  const [state, dispatch] = useReducer(tableWithViewReducer, initialState);
  const eventHandler = useEventHandler(props.componentId, props.events);

  // 视图选择处理
  const handleViewSelect = useCallback(async (viewKey: string, viewItem: ViewItem) => {
    dispatch({ type: ACTION_TYPES.SET_TABLE_LOADING, payload: true });
    dispatch({ type: ACTION_TYPES.SET_SELECTED_VIEW, payload: viewKey });

    try {
      // 触发视图选择事件
      eventHandler.onViewSelect({
        viewKey,
        viewItem,
        componentId: props.componentId,
        timestamp: Date.now()
      });

      // 根据视图的搜索条件加载表格数据
      const response = await eventHandler.onTableDataLoad({
        viewKey,
        searchConditions: viewItem.searchConditions || {},
        componentId: props.componentId,
        timestamp: Date.now()
      });

      const tableData = response?.data || [];
      dispatch({ type: ACTION_TYPES.SET_TABLE_DATA, payload: tableData });

      // 调用外部回调
      props.onViewSelect?.(viewKey, viewItem);
    } catch (error) {
      console.error('Failed to load table data:', error);
    } finally {
      dispatch({ type: ACTION_TYPES.SET_TABLE_LOADING, payload: false });
    }
  }, [eventHandler, props.componentId, props.onViewSelect]);

  // 组件挂载时加载视图数据
  useEffect(() => {
    const loadViewData = async () => {
      dispatch({ type: ACTION_TYPES.SET_VIEW_LOADING, payload: true });

      try {
        const response = await eventHandler.onMount({
          type: 'loadViews',
          componentId: props.componentId,
          timestamp: Date.now()
        });

        const viewData = response?.data || [];
        dispatch({ type: ACTION_TYPES.SET_VIEW_DATA, payload: viewData });

        // 如果有视图数据，默认选择第一个
        if (viewData.length > 0) {
          const firstView = viewData[0];
          dispatch({ type: ACTION_TYPES.SET_SELECTED_VIEW, payload: firstView.key });

          // 加载第一个视图的数据
          handleViewSelect(firstView.key, firstView);
        }
      } catch (error) {
        console.error('Failed to load view data:', error);
      } finally {
        dispatch({ type: ACTION_TYPES.SET_VIEW_LOADING, payload: false });
      }
    };

    loadViewData();
  }, [props.componentId, eventHandler, handleViewSelect]);

  // 表格搜索处理
  const handleTableSearch = useCallback(async (searchValues: Record<string, any>) => {
    dispatch({ type: ACTION_TYPES.SET_TABLE_LOADING, payload: true });

    try {
      // 触发搜索事件
      const response = await eventHandler.onSearch({
        searchValues,
        viewKey: state.selectedViewKey,
        componentId: props.componentId,
        timestamp: Date.now()
      });

      const tableData = response?.data || [];
      dispatch({ type: ACTION_TYPES.SET_TABLE_DATA, payload: tableData });

      // 调用外部回调
      props.onSearch?.(searchValues);
    } catch (error) {
      console.error('Failed to search table data:', error);
    } finally {
      dispatch({ type: ACTION_TYPES.SET_TABLE_LOADING, payload: false });
    }
  }, [eventHandler, state.selectedViewKey, props.componentId, props.onSearch]);

  // 视图搜索处理
  const handleViewSearch = useCallback((searchValue: string) => {
    // 触发视图搜索事件
    eventHandler.onViewSearch({
      searchValue,
      componentId: props.componentId,
      timestamp: Date.now()
    });
  }, [eventHandler, props.componentId]);

  return {
    // 视图状态
    viewState: {
      data: state.viewData,
      selectedKey: state.selectedViewKey,
      loading: state.viewLoading,
    },
    // 表格状态
    tableState: {
      data: state.tableData,
      loading: state.tableLoading,
    },
    // 事件处理器
    handleViewSelect,
    handleTableSearch,
    handleViewSearch,
    // 整体加载状态
    loading: state.loading,
  };
};
