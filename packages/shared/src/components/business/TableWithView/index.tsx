// 导出主组件
export { TableWithView } from './TableWithView';

// 导出类型定义
export type {
  TableWithViewProps,
  ViewItem,
  TableColumn,
  SearchField,
  ToolbarAction,
  ToolbarIcon,
  GroupTab,
  TableAction,
  PaginationConfig,
  RowSelectionConfig,
  QuickSearchConfig,
  ViewConfig,
  ViewPanelProps,
  TablePanelProps,
} from './types';

// 导出hooks
export { useTableWithView } from './hooks/useTableWithView';
export { useDataCache } from './hooks/useDataCache';

// 导出常量
export { CLASS_NAMES, EVENT_TYPES, DEFAULT_VIEW_WIDTH } from './constants';
