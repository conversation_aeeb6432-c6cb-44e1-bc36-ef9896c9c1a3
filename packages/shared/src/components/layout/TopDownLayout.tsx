import React from 'react';

export interface TopDownLayoutProps {
  header?: React.ReactNode;
  footer?: React.ReactNode;
  children?: React.ReactNode;
  headerHeight?: number;
  footerHeight?: number;
  style?: React.CSSProperties;
  className?: string;
}

export const TopDownLayout: React.FC<TopDownLayoutProps> = ({
  header,
  footer,
  children,
  headerHeight = 64,
  footerHeight = 32,
  style,
  className,
}) => {
  const layoutStyle: React.CSSProperties = {
    display: 'flex',
    flexDirection: 'column',
    height: '100vh',
    overflow: 'hidden',
    ...style,
  };

  const headerStyle: React.CSSProperties = {
    minHeight: `${headerHeight}px`,
    flexShrink: 0,
    zIndex: 1001,
    backgroundColor: '#fff',
    borderBottom: '1px solid #f0f0f0',
  };

  const mainStyle: React.CSSProperties = {
    flex: 1,
    overflow: 'hidden',
    minHeight: 0,
  };

  const footerStyle: React.CSSProperties = {
    height: `${footerHeight}px`,
    flexShrink: 0,
    zIndex: 999,
    backgroundColor: '#fff',
    borderTop: '1px solid #f0f0f0',
  };

  return (
    <div className={`lowcode-topdown-layout ${className || ''}`} style={layoutStyle}>
      {/* 头部 */}
      {header && (
        <header style={headerStyle}>
          {header}
        </header>
      )}

      {/* 主体区域 */}
      <main style={mainStyle}>
        {children}
      </main>

      {/* 底部 */}
      {footer && (
        <footer style={footerStyle}>
          {footer}
        </footer>
      )}
    </div>
  );
};
